import { useState, useEffect } from "react";
import { ModernCloc, useClocContext } from "@cloc/atoms";
import { ThemedButton } from "@cloc/ui";

function RobustTimerComponent() {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { isRunning, startTimer, stopTimer, loadings } = useClocContext();

  useEffect(() => {
    // Simulate initialization
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleTimerAction = async () => {
    try {
      setError(null);
      if (isRunning) {
        await stopTimer();
      } else {
        await startTimer();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    }
  };

  if (isLoading || loadings.timerStatusLoading) {
    return (
      <div className="p-6 text-center">
        <p className="mt-2 text-gray-600">Loading timer...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-semibold">Error</h3>
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => setError(null)}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <ModernCloc
        expanded={true}
        className="min-w-[300px]"
        showProgress={true}
      />

      <ThemedButton
        onClick={handleTimerAction}
        disabled={loadings.timerStatusLoading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loadings.timerStatusLoading
          ? "Processing..."
          : isRunning
          ? "Stop"
          : "Start"}
      </ThemedButton>
    </div>
  );
}

export default function AppWithErrorHandling() {
  return (
    <div className="max-w-4xl mx-auto p-3">
      <h1 className="text-2xl font-bold mb-6">Robust Timer App</h1>
      <RobustTimerComponent />
    </div>
  );
}
