---
id: theming
title: Theming & Customization
sidebar_label: Theming
sidebar_position: 8
---

# Theming & Customization

Customize the look and feel of your Ever Cloc implementation with our comprehensive theming system. Whether you're building a branded time tracking solution or integrating Ever Cloc into your existing application, our flexible theming approach gives you complete control over the visual experience.

## 🎨 Theme System Overview

Ever Cloc's theming system is built on modern CSS custom properties (CSS variables) and provides:

- **Consistent Design Language**: Unified color palette and typography across all components
- **Dark/Light Mode Support**: Automatic theme switching with user preferences
- **Brand Customization**: Easy integration of your company's visual identity
- **Component-Level Control**: Fine-grained customization for individual components
- **Responsive Design**: Mobile-first approach with adaptive layouts

## 🌈 Color System

### Primary Colors

The Ever Cloc theme system uses a semantic color approach:

```css
:root {
  /* Primary Brand Colors */
  --cloc-primary: #007bff;
  --cloc-primary-hover: #0056b3;
  --cloc-primary-light: #cce7ff;
  
  /* Secondary Colors */
  --cloc-secondary: #6c757d;
  --cloc-secondary-hover: #545b62;
  
  /* Success, Warning, Error */
  --cloc-success: #28a745;
  --cloc-warning: #ffc107;
  --cloc-error: #dc3545;
  
  /* Neutral Colors */
  --cloc-background: #ffffff;
  --cloc-surface: #f8f9fa;
  --cloc-border: #dee2e6;
  --cloc-text: #212529;
  --cloc-text-secondary: #6c757d;
}
```

### Dark Mode

```css
[data-theme="dark"] {
  --cloc-background: #1a1a1a;
  --cloc-surface: #2d2d2d;
  --cloc-border: #404040;
  --cloc-text: #ffffff;
  --cloc-text-secondary: #b0b0b0;
}
```

## 🎯 Component Theming

### Timer Components

Customize timer appearance with component-specific variables:

```css
.cloc-timer {
  --timer-background: var(--cloc-surface);
  --timer-border: var(--cloc-border);
  --timer-text: var(--cloc-text);
  --timer-primary: var(--cloc-primary);
  --timer-radius: 8px;
  --timer-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```

### Button Components

```css
.cloc-button {
  --button-padding: 12px 24px;
  --button-radius: 6px;
  --button-font-weight: 500;
  --button-transition: all 0.2s ease;
}
```

### Form Elements

```css
.cloc-input {
  --input-padding: 12px 16px;
  --input-border: 1px solid var(--cloc-border);
  --input-radius: 6px;
  --input-focus-border: var(--cloc-primary);
}
```

## 🛠️ Implementation Guide

### 1. CSS Custom Properties

Create a custom theme file:

```css
/* custom-theme.css */
:root {
  /* Override default colors */
  --cloc-primary: #your-brand-color;
  --cloc-primary-hover: #your-brand-color-dark;
  
  /* Custom spacing */
  --cloc-spacing-xs: 4px;
  --cloc-spacing-sm: 8px;
  --cloc-spacing-md: 16px;
  --cloc-spacing-lg: 24px;
  --cloc-spacing-xl: 32px;
  
  /* Typography */
  --cloc-font-family: 'Your Font', sans-serif;
  --cloc-font-size-base: 16px;
  --cloc-line-height-base: 1.5;
}
```

### 2. React Component Integration

Use the theme context in your React components:

```tsx
import { useTheme } from '@cloc/ui';

function CustomTimer() {
  const { theme, colors } = useTheme();
  
  return (
    <div 
      className="custom-timer"
      style={{
        backgroundColor: colors.surface,
        color: colors.text,
        borderRadius: theme.borderRadius.md
      }}
    >
      {/* Timer content */}
    </div>
  );
}
```

### 3. Theme Provider Setup

Wrap your application with the theme provider:

```tsx
import { ThemeProvider } from '@cloc/ui';

function App() {
  return (
    <ThemeProvider
      theme={{
        colors: {
          primary: '#your-brand-color',
          secondary: '#your-secondary-color'
        },
        spacing: {
          xs: '4px',
          sm: '8px',
          md: '16px'
        }
      }}
    >
      <YourApp />
    </ThemeProvider>
  );
}
```

## 🎨 Brand Customization

### Logo Integration

Replace the default Ever Cloc logo with your brand:

```tsx
import { ClocProvider } from '@cloc/atoms';

<ClocProvider
  config={{
    branding: {
      logo: '/path/to/your-logo.svg',
      logoDark: '/path/to/your-logo-dark.svg',
      favicon: '/path/to/your-favicon.ico'
    }
  }}
>
  <YourApp />
</ClocProvider>
```

### Custom Fonts

```css
@import url('https://fonts.googleapis.com/css2?family=YourFont:wght@400;500;600&display=swap');

:root {
  --cloc-font-family: 'YourFont', -apple-system, BlinkMacSystemFont, sans-serif;
}
```

## 📱 Responsive Design

### Breakpoints

```css
:root {
  --cloc-breakpoint-sm: 576px;
  --cloc-breakpoint-md: 768px;
  --cloc-breakpoint-lg: 992px;
  --cloc-breakpoint-xl: 1200px;
}

@media (max-width: 768px) {
  .cloc-timer {
    --timer-padding: 16px;
    --timer-font-size: 14px;
  }
}
```

### Mobile Optimization

```css
.cloc-mobile {
  --mobile-padding: 16px;
  --mobile-font-size: 14px;
  --mobile-button-height: 44px; /* iOS recommended touch target */
}
```

## 🌙 Dark Mode Implementation

### Automatic Theme Detection

```tsx
import { useColorScheme } from '@cloc/ui';

function ThemeToggle() {
  const { colorScheme, setColorScheme } = useColorScheme();
  
  return (
    <button 
      onClick={() => setColorScheme(
        colorScheme === 'light' ? 'dark' : 'light'
      )}
    >
      {colorScheme === 'light' ? '🌙' : '☀️'}
    </button>
  );
}
```

### System Preference Detection

```css
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}
```

## 🎯 Advanced Customization

### Custom Component Variants

```tsx
import { styled } from '@cloc/ui';

const CustomTimer = styled(Timer)`
  background: linear-gradient(135deg, var(--cloc-primary), var(--cloc-secondary));
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
  }
`;
```

### Animation Customization

```css
:root {
  --cloc-transition-fast: 0.15s ease;
  --cloc-transition-normal: 0.3s ease;
  --cloc-transition-slow: 0.5s ease;
  
  --cloc-animation-bounce: bounce 0.6s ease-in-out;
  --cloc-animation-fade: fadeIn 0.3s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}
```

## 📚 Examples & Resources

### Complete Theme Example

Check out our [Storybook](https://storybook.cloc.ai) for interactive theme examples and component showcases.

### Community Themes

- **[Minimal Theme](https://github.com/ever-co/ever-cloc/tree/main/themes/minimal)** - Clean, distraction-free design
- **[Corporate Theme](https://github.com/ever-co/ever-cloc/tree/main/themes/corporate)** - Professional business appearance
- **[Dark Theme](https://github.com/ever-co/ever-cloc/tree/main/themes/dark)** - Complete dark mode implementation

## 🔧 Troubleshooting

### Common Issues

- **CSS Variables Not Working**: Ensure your browser supports CSS custom properties
- **Theme Not Applying**: Check CSS specificity and import order
- **Dark Mode Issues**: Verify `data-theme` attribute is set correctly
- **Font Loading**: Use `font-display: swap` for better performance

### Debug Tools

```css
/* Debug theme variables */
.debug-theme * {
  outline: 1px solid red !important;
}

.debug-theme::before {
  content: attr(style);
  position: fixed;
  top: 0;
  left: 0;
  background: white;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  z-index: 9999;
}
```

---

**Ready to customize?** Start with our [UI Components Guide](/docs/components/ui) to see all available customization options, or explore our [Examples & Tutorials](/docs/examples-tutorials) for real-world theming implementations.
