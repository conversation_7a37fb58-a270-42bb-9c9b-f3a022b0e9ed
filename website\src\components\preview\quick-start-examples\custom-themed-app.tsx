import {
  ClocProvider,
  ModernCloc,
  ClocThemeToggle,
  BasicClocReport,
} from "@cloc/atoms";

const customTheme = {
  colors: {
    textColor: "black",
    backgroundColor: "white",
    mainColor: "linear-gradient(135deg, blue 0%, red 100%)",
    borderColor: "blue",
  },
};

export default function CustomThemedApp() {
  return (
    <ClocProvider theme={customTheme}>
      <div className="flex flex-col gap-3  p-3">
        <div className="flex flex-col gap-2">
          Try pre-defined theme:
          <ClocThemeToggle />
        </div>
        <ModernCloc expanded={false} showProgress={true} variant="bordered" />
      </div>
      <BasicClocReport />
    </ClocProvider>
  );
}
