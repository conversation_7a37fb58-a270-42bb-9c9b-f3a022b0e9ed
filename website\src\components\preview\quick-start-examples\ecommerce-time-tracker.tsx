import { ModernCloc } from "@cloc/atoms";

export default function EcommerceTimeTracker() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Product Development Timer</h1>

      {/* Development Time Tracker */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-4 border dark:border-gray-600 rounded-lg">
          <h3 className="font-semibold mb-3">Frontend Development</h3>
          <ModernCloc expanded={true} showProgress={true} />
        </div>

        <div className="p-4 border dark:border-gray-600 rounded-lg">
          <h3 className="font-semibold mb-3">Backend Development</h3>
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="default"
            size="default"
          />
        </div>
      </div>
    </div>
  );
}
