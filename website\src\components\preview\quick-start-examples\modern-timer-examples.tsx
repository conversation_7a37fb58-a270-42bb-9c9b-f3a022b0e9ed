import { ModernCloc } from "@cloc/atoms";

export default function ModernTimerExamples() {
  return (
    <div className="flex gap-3 flex-wrap">
      {/* Compact Timer */}
      <ModernCloc
        expanded={false}
        showProgress={true}
        variant="default"
        size="sm"
        className="max-w-[200px] h-fit"
      />
      {/* Expanded Timer with Controls */}
      <ModernCloc
        expanded={false}
        showProgress={true}
        variant="bordered"
        size="default"
        resizable={true}
        className="h-fit"
      />

      {/* Large Resizable Timer */}
      <ModernCloc
        expanded={true}
        showProgress={true}
        className="h-fit"
        variant="default"
        resizable={true}
        draggable={true}
      />
    </div>
  );
}
