import React from "react";

export type ThemedButtonProps =
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "outline" | "ghost";
  };

export const ThemedButton: React.FC<ThemedButtonProps> = ({
  variant = "default",
  className,
  ...props
}) => {
  const base =
    "inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none";
  const stylesByVariant: Record<string, string> = {
    default: "bg-primary text-primary-foreground hover:opacity-90",
    outline: "border border-border bg-transparent hover:bg-muted",
    ghost: "bg-transparent hover:bg-muted",
  };
  const classes = [
    base,
    stylesByVariant[variant] ?? stylesByVariant.default,
    className,
  ]
    .filter(Boolean)
    .join(" ");
  return <button className={classes} {...props} />;
};
