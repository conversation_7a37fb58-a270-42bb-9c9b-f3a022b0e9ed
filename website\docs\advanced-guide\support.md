---
id: support
title: Support & Help
sidebar_label: Support
sidebar_position: 1
---

# Support & Help

Welcome to the Ever Cloc support center. We're here to help you get the most out of your time tracking and productivity management experience.

## 🆘 Getting Help

### Community Support

- **🐛 [GitHub Issues](https://github.com/ever-co/ever-cloc/issues)** - Report bugs, request features, or ask technical questions
- **💬 [Discord Community](https://discord.com/invite/msqRJ4w)** - Join our active Discord server for real-time support
- **💬 [Gitter Chat](https://gitter.im/ever-co/ever-cloc)** - Chat with the community and developers
- **📚 [Stack Overflow](https://stackoverflow.com/questions/tagged/ever-cloc)** - Ask technical questions with the `ever-cloc` tag

### Professional Support

- **📧 [Email Support](mailto:<EMAIL>)** - Direct support for business inquiries
- **🔒 [Security Issues](mailto:<EMAIL>)** - Report security vulnerabilities privately
- **💼 [Enterprise Support](https://ever.co/contacts)** - Dedicated support for enterprise customers

## 📖 Documentation Resources

### Getting Started
- **[Installation Guide](/docs/introduction/installation-guide)** - Complete setup instructions
- **[Quick Start Example](/docs/introduction/quick-start-example)** - Get up and running quickly
- **[API Documentation](/docs/api)** - Integrate with your existing tools

### Core Libraries
- **[@cloc/atoms](/docs/core-libraries/cloc-atoms)** - React components and UI elements
- **[@cloc/ui](/docs/core-libraries/cloc-ui)** - Core UI component library
- **[@cloc/tracking](/docs/core-libraries/cloc-tracking)** - Analytics and tracking capabilities
- **[@cloc/api](/docs/core-libraries/cloc-api)** - API client and utilities

### Components & Examples
- **[Timer Components](/docs/components/timer)** - Time tracking components
- **[UI Components](/docs/components/ui)** - Interface building blocks
- **[Examples & Tutorials](/docs/examples-tutorials)** - Real-world implementation examples

## 🚀 Platform Access

### Cloud Platforms
- **🌐 [Production Platform](https://app.cloc.ai)** - Live Ever Cloc instance
- **🧪 [Demo Environment](https://demo.gauzy.co)** - Try features before deployment

### Self-Hosted Options
- **🐳 [Docker Deployment](https://github.com/ever-co/ever-cloc#docker)** - Container-based installation
- **☁️ [Cloud Deployment](https://github.com/ever-co/ever-cloc#cloud-deployment)** - Deploy to your preferred cloud provider

## 🔧 Troubleshooting

### Common Issues

#### Installation Problems
- **Node.js Version**: Ensure you're using Node.js 18+
- **Dependencies**: Run `npm install` or `yarn install` in the website directory
- **Port Conflicts**: Use `--port` flag to specify a different port

#### Build Issues
- **TypeScript Errors**: Check your TypeScript configuration
- **Missing Dependencies**: Ensure all packages are properly installed
- **Environment Variables**: Verify your `.env` file configuration

#### Runtime Issues
- **API Connection**: Check your `CLOC_API_URL` environment variable
- **Authentication**: Verify your API credentials
- **Permissions**: Ensure proper file system permissions

### Debug Mode

Enable debug logging by setting environment variables:

```bash
DEBUG=cloc:*
NODE_ENV=development
```

## 📋 Feature Requests

Have an idea for improving Ever Cloc? We'd love to hear from you!

- **🎨 [Feature Requests](https://github.com/ever-co/feature-requests/issues)** - Submit your ideas
- **💡 [Roadmap](https://github.com/ever-co/ever-cloc/projects)** - See what's planned
- **🤝 [Contributing Guide](/docs/introduction/installation-guide)** - Help us build Ever Cloc

## 🏢 Enterprise Support

For enterprise customers, we offer:

- **Priority Support**: Dedicated support channels
- **Custom Integrations**: Tailored solutions for your needs
- **Training & Onboarding**: Get your team up to speed quickly
- **SLA Guarantees**: Service level agreements for critical deployments

Contact us at [<EMAIL>](mailto:<EMAIL>) for enterprise support options.

## 📞 Contact Information

- **Website**: [ever.co](https://ever.co)
- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **Twitter**: [@ever_cloc](https://twitter.com/ever_cloc)
- **Facebook**: [Ever Cloc HQ](https://www.facebook.com/everclochq)

---

**Need immediate help?** Join our [Discord community](https://discord.com/invite/msqRJ4w) for real-time support from the Ever Cloc team and community.
