import { ClocProvider } from "@cloc/atoms";
import useDocusaurusContext from "@docusaurus/useDocusaurusContext";

export default function Root({ children }) {
  const { siteConfig } = useDocusaurusContext();
  const apiUrl = siteConfig?.customFields?.CLOC_API_URL as string;

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <ClocProvider config={{ apiUrl }}>{children}</ClocProvider>
      </body>
    </html>
  );
}
