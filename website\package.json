{"name": "ever-cloc-docs", "version": "0.0.0", "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "scripts": {"docusaurus": "docusaurus start", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "version": "npx docusaurus --version", "spellcheck": "cspell ."}, "dependencies": {"@docusaurus/core": "3.9.0", "@docusaurus/preset-classic": "3.9.0", "@docusaurus/theme-common": "3.9.0", "@docusaurus/theme-search-algolia": "^3.9.0", "@easyops-cn/docusaurus-search-local": "^0.46.1", "@mdx-js/react": "^3.0.0", "cheerio": "^1.0.0", "clsx": "^2.0.0", "docusaurus-plugin-sentry": "^2.0.0", "dotenv": "^16.4.5", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-player": "^2.16.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.9.0", "@docusaurus/tsconfig": "3.9.0", "@docusaurus/types": "3.9.0", "autoprefixer": "^10.4.21", "cspell": "^8.6.0", "postcss": "^8.5.3", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}