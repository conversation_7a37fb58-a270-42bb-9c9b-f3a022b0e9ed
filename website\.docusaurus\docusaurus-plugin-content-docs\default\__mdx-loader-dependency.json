{"options": {"exclude": ["**/i18n/**"], "sidebarPath": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\sidebars.ts", "path": "./docs/", "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/", "editCurrentVersion": false, "editLocalizedFiles": false, "routeBasePath": "docs", "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "showLastUpdateTime": false, "showLastUpdateAuthor": false, "includeCurrentVersion": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/docs", "tagsPath": "/docs/tags", "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs", "editUrlLocalized": "https://github.com/ever-co/ever-cloc/tree/main/docs/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\sidebars.ts", "contentPath": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\docs", "contentPathLocalized": "C:\\Users\\<USER>\\Desktop\\Projects\\Ever Tech\\ever-cloc-docs\\website\\docs\\i18n\\en\\docusaurus-plugin-content-docs\\current"}]}