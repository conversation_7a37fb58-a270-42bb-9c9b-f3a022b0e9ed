import {
  ModernCloc,
  ClocLoginDialog,
  ClocThemeToggle,
  DailyActivityDisplayer,
  WorkedProjectDisplayer,
  WeeklyWorkedTimeDisplayer,
  DailyWorkedTimeDisplayer,
  ClocLanguageSwitch,
  BasicClocReport,
  ClocAppsUrlList,
} from "@cloc/atoms";

function TimerDashboard() {
  return (
    <div className="container p-6 mx-auto space-y-8">
      <header className="flex justify-between items-center">
        <h1 className="p-0 m-0 text-3xl font-bold">Dashboard</h1>
        <div className="flex justify-center items-center space-x-4">
          <ClocThemeToggle />
          <ClocLanguageSwitch />
          <ClocLoginDialog />
        </div>
      </header>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col col-span-1 gap-2">
          <DailyActivityDisplayer />
          <WorkedProjectDisplayer />
          <WeeklyWorkedTimeDisplayer />
          <DailyWorkedTimeDisplayer />
        </div>

        <div className="col-span-1 md:col-span-2 lg:col-span-1">
          <ModernCloc
            className="min-w-[300px]"
            expanded={true}
            showProgress={true}
          />
        </div>
      </div>
      <ClocAppsUrlList />
      <BasicClocReport type="line" />
    </div>
  );
}

export default function AdvancedTimerComponent() {
  return <TimerDashboard />;
}
