import { BasicTimer } from "@cloc/atoms";

export default function CustomTimerExamples() {
  return (
    <div className="flex gap-10 flex-col md:flex-row ">
      <div>
        Primary
        <BasicTimer
          background="primary"
          color="secondary"
          border="thick"
          icon={true}
        />
      </div>
      <div>
        Secondary
        <BasicTimer
          background="secondary"
          color="primary"
          border="thin"
          icon={false}
        />
      </div>
      <div>
        Destructive
        <BasicTimer
          background="destructive"
          color="primary"
          border="none"
          readonly={true}
        />
      </div>
    </div>
  );
}
